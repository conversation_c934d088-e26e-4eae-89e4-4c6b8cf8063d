#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
(1) Uses an LLM to map Benefit Drivers → APQC metric IDs.
(2) Converts the mapping into Low/Med/High benefit-rate %s.

Run:
  python driver_rate_engine_llm.py --apqc APQC_Finance.csv \
        --drivers benefit_drivers.txt --out RateCard_LLM.xlsx \
        --rev "1,000 - 5,000" --baseline 75

Author : <PERSON>
Date   : 2025-06-17
"""

import os, re, json, argparse, warnings, collections
from pathlib import Path
import pandas as pd
from tabulate import tabulate
import openai, tiktoken

warnings.simplefilter("ignore", UserWarning)

# ---------------------------------------------------------------- CONFIG ----
OPENAI_MODEL      = "gpt-4.1"   # or gpt-4-turbo
MAX_CANDIDATES    = 18                # short-list size fed to the LLM
DEFAULT_REVBAND   = "1,000 - 5,000"
DEFAULT_BASELINE  = 75                # 25 / 50 / 75
OUTPUT_XLSX       = "RateCard_LLM.xlsx"

# ---------------------------------------------------------------- HELPERS ----
def short_metric_text(row):
    """Build a one-liner used in the prompt (saves tokens)."""
    bits = [str(row['METRIC ID']),
            row['Research Area'],
            row['Category Description'],
            row['ORIG METRIC DESCRIPTION']]
    return " | ".join(str(b) for b in bits if pd.notna(b))[:200]   # trim

def token_len(msg, model=OPENAI_MODEL):
    enc = tiktoken.encoding_for_model(model)
    return len(enc.encode(msg))

def openai_chat(system, user, model=OPENAI_MODEL):
    resp = openai.chat.completions.create(
        model=model,
        messages=[{"role":"system","content":system},
                  {"role":"user",  "content":user}],
        temperature=0)
    return resp.choices[0].message["content"]

def quartile_to_rates(p25,p50,p75,baseline):
    base = {25:p25,50:p50,75:p75}[baseline]
    if base==0: return 0,0,0
    low  = (base-p50)/base
    med  = (base-p25)/base
    wc   = p25 - (p50-p25)/2
    high = (base-wc)/base
    return round(low,3),round(med,3),round(high,3)

# ---------------------------------------------------------------- LLM MAP ----
def build_mapping_llm(drivers, apqc_df, rev_band):
    """
    Returns dataframe: driver | metric_ids | weights
    """
    print(f"\n[1/3]  Asking LLM to map {len(drivers)} drivers ...")

    rows=[]
    # Pre-build searchable catalogue (lower-case for fast keyword match)
    apqc_df['prompt_text'] = apqc_df.apply(short_metric_text,axis=1)
    cat = dict(zip(apqc_df['METRIC ID'], apqc_df['prompt_text'].str.lower()))

    inverted=collections.defaultdict(set)
    for mid,txt in cat.items():
        for word in re.findall(r'[a-z]+',txt):
            inverted[word].add(mid)

    for drv in drivers:
        # 1. crude keyword filter  → candidate IDs
        words = re.findall(r'[a-z]+',drv.lower())
        cand   = collections.Counter()
        for w in words:
            for mid in inverted.get(w, []):
                cand[mid]+=1
        # keep top-n by keyword count
        cand_ids=[mid for mid,_ in cand.most_common(MAX_CANDIDATES)]
        if not cand_ids:                # fallback: random 18
            cand_ids=list(cat.keys())[:MAX_CANDIDATES]

        # 2. Build prompt
        list_lines=[f"{mid}: {cat[mid]}" for mid in cand_ids]
        system_msg = ("You are a finance process benchmarking expert. "
                      "From the candidate APQC metrics provided, pick the "
                      "1-3 IDs that best MEASURE the specified Benefit "
                      "Driver’s cost pool. Return JSON like "
                      '{"metric_ids":["101108"],"weights":[1.0]} . If none '
                      "are good, return an empty list.")
        user_msg = (f"Benefit Driver: {drv}\n\n"
                    f"Candidate APQC metrics (ID: text):\n" +
                    "\n".join(list_lines) +
                    "\n\nRules:\n"
                    "1. Only use IDs from the list. 2. Up to three IDs. "
                    "3. weights must sum to 1.0.")
        # Ensure we stay within model token limit (trim list if not)
        while token_len(user_msg)>6000:   # safety for gpt-3.5-turbo
            cand_ids=cand_ids[:-3]
            list_lines=[f"{mid}: {cat[mid]}" for mid in cand_ids]
            user_msg = (f"Benefit Driver: {drv}\n\nCandidate APQC metrics:\n" +
                        "\n".join(list_lines) + "\n\nRules as above.")
        # 3. Call LLM
        try:
            ans=openai_chat(system_msg,user_msg)
            data=json.loads(ans)
            mids=data.get("metric_ids",[])
            wts =data.get("weights",[])
        except Exception as e:
            print(f"⚠️  LLM mapping failed for '{drv}': {e}")
            mids,wts=[],[]
        if mids and (not wts or len(wts)!=len(mids)):
            wts=[1/len(mids)]*len(mids)
        rows.append({
            "Benefit_Driver_Name":drv,
            "APQC_Metric_IDs":",".join(mids),
            "Metric_Weights":",".join(str(round(w,3)) for w in wts)
        })
    map_df=pd.DataFrame(rows)
    # add ID column
    map_df["Benefit_Driver_ID"]=map_df["Benefit_Driver_Name"].str\
        .replace('[^A-Za-z0-9]+','_',regex=True).str.upper().str[:50]
    map_df["Revenue_Band_Default"]=rev_band
    map_df["Baseline_Quartile"]=DEFAULT_BASELINE
    return map_df

# ---------------------------------------------------------------- RATES  ----
def build_rate_card(mapping_df, apqc_q, rev_band, baseline_q):
    out_rows=[]
    for _,r in mapping_df.iterrows():
        mids = [m.strip() for m in r.APQC_Metric_IDs.split(',') if m.strip()]
        if not mids: continue
        wts  = [float(x) for x in r.Metric_Weights.split(',')] \
               if r.Metric_Weights else [1/len(mids)]*len(mids)
        # pull quartiles
        p25=p50=p75=0
        for mid,w in zip(mids,wts):
            try:
                row=apqc_q.loc[(mid,rev_band)]
            except KeyError:
                print(f"⚠️  Metric {mid} missing for band {rev_band}")
                continue
            p25+=row.p25*w; p50+=row.p50*w; p75+=row.p75*w
        low,med,high = quartile_to_rates(p25,p50,p75,baseline_q)
        out_rows.append({
            "Benefit_Driver_ID":r.Benefit_Driver_ID,
            "Benefit_Driver_Name":r.Benefit_Driver_Name,
            "Revenue_Band_Used":rev_band,
            "Baseline_Qrt":baseline_q,
            "Low_%":low, "Med_%":med,"High_%":high
        })
    return pd.DataFrame(out_rows)

# ----------------------------------------------------------------  MAIN  ----
def main(apqc_csv, driver_file, out_xlsx, rev_band, baseline_q):
    openai.api_key = "********************************************************************************************************************************************************************"
    if not openai.api_key:
        raise EnvironmentError("OPENAI_API_KEY not set.")

    # 0) load APQC
    apqc=pd.read_csv(apqc_csv,dtype={'METRIC ID':str})
    apqc=apqc[apqc['REVENUE RANGE ($M)']==rev_band].copy()
    apqc_q=apqc[['METRIC ID','REVENUE RANGE ($M)',
                 ' ORIG 25TH PCT ',' ORIG 50TH PCT ',' ORIG 75TH PCT ']]\
            .rename(columns={'METRIC ID':'metric_id',
                             'REVENUE RANGE ($M)':'band',
                             ' ORIG 25TH PCT ':'p25',
                             ' ORIG 50TH PCT ':'p50',
                             ' ORIG 75TH PCT ':'p75'})
    apqc_q.set_index(['metric_id','band'],inplace=True)

    # 1) load driver names
    drivers=[l.strip() for l in Path(driver_file).read_text(encoding="utf-8").splitlines()
             if l.strip()]
    # 2) LLM mapping
    mapping_df=build_mapping_llm(drivers,apqc,rev_band)
    # 3) rate card
    rate_df=build_rate_card(mapping_df,apqc_q,rev_band,baseline_q)

    # 4) save
    with pd.ExcelWriter(out_xlsx,engine='openpyxl') as xl:
        mapping_df.to_excel(xl,index=False,sheet_name="LLM_Mapping")
        rate_df.to_excel(xl,index=False,sheet_name="Benefit_Rate_Card")
    print("\n=== Preview of Benefit Rate Card ===")
    print(tabulate(rate_df.head(10),headers="keys",showindex=False,
                   floatfmt=".1%"))
    print(f"\n[✅] Mapping + rate card written to {out_xlsx}\n")

# ----------------------------------------------------------------- CLI  ----
if __name__=="__main__":
    parser=argparse.ArgumentParser(description="LLM-driven Driver→APQC mapping + rate card")
    parser.add_argument("--apqc", required=True, help="APQC_Finance.csv")
    parser.add_argument("--drivers", required=True, help="benefit_drivers.txt")
    parser.add_argument("--out", default=OUTPUT_XLSX, help="Output Excel path")
    parser.add_argument("--rev", default=DEFAULT_REVBAND, help="Revenue band text")
    parser.add_argument("--baseline", type=int, choices=[25,50,75],
                        default=DEFAULT_BASELINE, help="Baseline quartile")
    args=parser.parse_args()
    main(args.apqc,args.drivers,args.out,args.rev,args.baseline)